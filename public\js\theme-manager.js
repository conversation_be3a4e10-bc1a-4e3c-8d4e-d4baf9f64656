/**
 * UniLink Theme Manager
 * Handles dark/light mode switching with persistence
 */

class ThemeManager {
    constructor() {
        this.currentTheme = this.getStoredTheme() || this.getSystemTheme();
        this.init();
    }

    init() {
        // Make theme manager globally available first
        window.themeManager = this;

        // Apply initial theme
        this.applyTheme(this.currentTheme);

        // Listen for system theme changes
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                if (!this.hasStoredTheme()) {
                    this.setTheme(e.matches ? 'dark' : 'light');
                }
            });
        }
    }

    getSystemTheme() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
        }
        return 'light';
    }

    getStoredTheme() {
        return localStorage.getItem('unilink-theme');
    }

    hasStoredTheme() {
        return localStorage.getItem('unilink-theme') !== null;
    }

    setTheme(theme) {
        this.currentTheme = theme;
        this.applyTheme(theme);
        this.storeTheme(theme);
        this.syncWithServer(theme);
        
        // Dispatch custom event for components to listen to
        window.dispatchEvent(new CustomEvent('theme-changed', { 
            detail: { theme: theme } 
        }));
    }

    applyTheme(theme) {
        const html = document.documentElement;
        
        if (theme === 'dark') {
            html.classList.add('dark');
        } else {
            html.classList.remove('dark');
        }

        // Add theme transition class temporarily
        html.classList.add('theme-transition');
        setTimeout(() => {
            html.classList.remove('theme-transition');
        }, 300);
    }

    storeTheme(theme) {
        localStorage.setItem('unilink-theme', theme);
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
        return newTheme;
    }

    getCurrentTheme() {
        return this.currentTheme;
    }

    isDark() {
        return this.currentTheme === 'dark';
    }

    isLight() {
        return this.currentTheme === 'light';
    }

    // Sync theme preference with server
    async syncWithServer(theme) {
        try {
            const response = await fetch('/api/user/theme-preference', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({ theme: theme })
            });

            if (!response.ok) {
                console.warn('Server responded with error:', response.status);
            }
        } catch (error) {
            console.warn('Failed to sync theme preference with server:', error);
        }
    }

    // Get theme preference from server on page load
    async loadServerTheme() {
        try {
            const response = await fetch('/api/user/theme-preference', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                if (data.theme && data.theme !== this.currentTheme) {
                    this.setTheme(data.theme);
                }
            }
        } catch (error) {
            console.warn('Failed to load theme preference from server:', error);
        }
    }
}

// Initialize theme manager when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new ThemeManager();
});

// Alpine.js data for theme toggle component
document.addEventListener('alpine:init', () => {
    Alpine.data('themeToggle', () => ({
        isDark: false,

        init() {
            // Wait for theme manager to be ready
            const checkThemeManager = () => {
                if (window.themeManager) {
                    this.isDark = window.themeManager.isDark();
                } else {
                    // Check if dark class is present on html element
                    this.isDark = document.documentElement.classList.contains('dark');
                    // Retry after a short delay
                    setTimeout(checkThemeManager, 100);
                }
            };

            checkThemeManager();

            // Listen for theme changes
            window.addEventListener('theme-changed', (e) => {
                this.isDark = e.detail.theme === 'dark';
            });
        },

        toggle() {
            if (window.themeManager) {
                const newTheme = window.themeManager.toggleTheme();
                this.isDark = newTheme === 'dark';
            } else {
                // Fallback toggle if theme manager isn't ready
                const html = document.documentElement;
                if (html.classList.contains('dark')) {
                    html.classList.remove('dark');
                    this.isDark = false;
                    localStorage.setItem('unilink-theme', 'light');
                } else {
                    html.classList.add('dark');
                    this.isDark = true;
                    localStorage.setItem('unilink-theme', 'dark');
                }
            }
        }
    }));
});
