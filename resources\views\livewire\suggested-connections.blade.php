<div>
    <div class="bg-white dark:bg-gray-800 rounded-lg border-opacity-20 shadow-sm pr-4 theme-transition">
        <!-- Header with refresh button -->
        <div class="flex items-center justify-between px-0 py-4 border-b border-custom-second-darkest dark:border-gray-700 border-opacity-10 theme-transition">
            <h3 class="text-lg font-bold text-custom-darkest dark:text-gray-200 theme-transition">Suggested Connection</h3>
            <button
                wire:click="refreshSuggestions"
                wire:loading.attr="disabled"
                class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 theme-transition"
                title="Refresh suggestions">
                <svg wire:loading.remove wire:target="refreshSuggestions" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <svg wire:loading wire:target="refreshSuggestions" class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </button>
        </div>

        <!-- Scrollable Suggestions List -->
        <div class="space-y-1 text-sm overflow-y-auto px-0 py-3 custom-scrollbar">
            @if($isLoading)
                <!-- Loading State -->
                @for($i = 0; $i < 3; $i++)
                    <div class="border-b border-custom-second-darkest border-opacity-20 pb-2 {{ $i === 2 ? 'border-b-0 pb-0' : '' }}">
                        <div class="animate-pulse flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gray-200 rounded-full"></div>
                            <div class="flex-1 space-y-1">
                                <div class="h-3 bg-gray-200 rounded w-3/4"></div>
                                <div class="h-2 bg-gray-200 rounded w-1/2"></div>
                            </div>
                            <div class="w-12 h-6 bg-gray-200 rounded"></div>
                        </div>
                    </div>
                @endfor
            @elseif(empty($suggestedUsers))
                <!-- Empty State -->
                <div class="text-center py-4">
                    <svg class="w-8 h-8 text-gray-300 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <p class="text-gray-500 text-xs">No suggestions available</p>
                </div>
            @else
                <!-- Suggested Organizations List -->
                @foreach($suggestedUsers as $index => $suggestion)
                    <div wire:key="suggestion-{{ $suggestion['user']->id }}" class="border-b border-custom-second-darkest dark:border-gray-700 border-opacity-20 pb-2 {{ $index === count($suggestedUsers) - 1 ? 'border-b-0 pb-0' : '' }} hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors rounded-lg theme-transition">
                        <div class="flex items-center space-x-3">
                            <!-- User Avatar -->
                            <a href="{{ route('profile.user', $suggestion['user']) }}" class="flex-shrink-0">
                                <img class="w-10 h-10 rounded-full object-cover border border-custom-green shadow-sm"
                                     src="{{ $suggestion['user']->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($suggestion['user']->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($suggestion['user']->name) . '&color=7BC74D&background=EEEEEE' }}"
                                     alt="{{ $suggestion['user']->name }}">
                            </a>

                            <!-- User Info -->
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <a href="{{ route('profile.user', $suggestion['user']) }}"
                                       class="font-semibold text-custom-darkest hover:text-custom-green transition-colors truncate text-sm">
                                        {{ $suggestion['user']->name }}
                                    </a>

                                    <!-- Follow Button -->
                                    <div class="ml-2">
                                        @auth
                                            @if(auth()->id() !== $suggestion['user']->id)
                                                @php
                                                    $isFollowing = auth()->user()->isFollowingUser($suggestion['user']);
                                                @endphp
                                                <button
                                                    wire:click="toggleFollow({{ $suggestion['user']->id }})"
                                                    wire:loading.attr="disabled"
                                                    wire:target="toggleFollow({{ $suggestion['user']->id }})"
                                                    class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full transition-colors disabled:opacity-50 {{ $isFollowing ? 'bg-gray-100 text-gray-700 hover:bg-gray-200' : 'bg-blue-600 text-white hover:bg-blue-700' }}">

                                                    @if($isFollowing)
                                                        <span wire:loading.remove wire:target="toggleFollow({{ $suggestion['user']->id }})">Following</span>
                                                    @else
                                                        <span wire:loading.remove wire:target="toggleFollow({{ $suggestion['user']->id }})">Follow</span>
                                                    @endif

                                                    <span wire:loading wire:target="toggleFollow({{ $suggestion['user']->id }})">
                                                        <svg class="animate-spin w-3 h-3" fill="none" viewBox="0 0 24 24">
                                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                        </svg>
                                                    </span>
                                                </button>
                                            @endif
                                        @else
                                            <a href="{{ route('login') }}"
                                               class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded hover:bg-blue-100 transition-colors">
                                                Follow
                                            </a>
                                        @endauth
                                    </div>
                                </div>

                                <!-- User Role/Status -->
                                <p class="text-xs text-custom-second-darkest">
                                    {{ $suggestion['user']->year_level ? $suggestion['user']->year_level . ' • ' : '' }}
                                    {{ ucfirst(str_replace('_', ' ', $suggestion['user']->role)) }}
                                </p>

                                <!-- Shared Attributes -->
                                @if(!empty($suggestion['shared_attributes']))
                                    <div class="mt-1">
                                        @foreach(array_slice($suggestion['shared_attributes'], 0, 1) as $attribute)
                                            <p class="text-xs text-blue-600">
                                                {{ $attribute }}
                                            </p>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            @endif
        </div>

        <!-- View All Link (footer, not scrollable) -->
        <div class="mt-3 pt-2 border-t border-custom-second-darkest border-opacity-20 px-4 pb-3">
            <a href="{{ route('follow-management.following', ['tab' => 'discover']) }}"
               class="text-xs text-custom-green hover:text-custom-second-darkest font-medium flex items-center justify-center">
                <span>Discover More People</span>
                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </a>
        </div>
    </div>

    <style>
        /* Custom scrollbar for suggestions list */
        .custom-scrollbar::-webkit-scrollbar {
            width: 8px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #7BC74D;
            border-radius: 4px;
            border: 1px solid #f1f5f9;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #6bb240;
        }
        .custom-scrollbar {
            scrollbar-width: thin;
            scrollbar-color: #7BC74D #f1f5f9;
        }
    </style>
</div>
