<?php

use App\Http\Controllers\Api\SchoolController;
use App\Http\Controllers\Api\ThemeController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// School and Campus API routes
Route::get('/schools', [SchoolController::class, 'index']);
Route::get('/schools/{school}/campuses', [SchoolController::class, 'getCampuses']);

// Theme preference routes (authenticated users only)
Route::middleware(['web', 'auth'])->group(function () {
    Route::get('/user/theme-preference', [ThemeController::class, 'getThemePreference']);
    Route::post('/user/theme-preference', [ThemeController::class, 'updateThemePreference']);
});
